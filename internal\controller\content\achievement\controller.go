package achievement

import (
	"context"
	"net/http"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	achievement "github.com/dsoplabs/dinbora-backend/internal/service/content/achievement"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// CRUD
	Create() echo.HandlerFunc
	Find() echo.HandlerFunc
	FindAll() echo.HandlerFunc
	FindByIdentifier() echo.HandlerFunc
	Update() echo.HandlerFunc
	Delete() echo.HandlerFunc
}

type controller struct {
	Service achievement.Service
}

func New(service achievement.Service) Controller {
	return &controller{
		Service: service,
	}
}

func (ac *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	achievementContentGroup := legacyGroup.Group("content/trophy/")
	achievementsContentGroup := currentGroup.Group("/contents/achievements")

	// CRUD
	achievementContentGroup.POST("", ac.Create(), middlewares.AuthGuard(), middlewares.AdminGuard())
	achievementContentGroup.GET(":id/", ac.Find())
	achievementContentGroup.GET("findAll/", ac.FindAll())
	achievementContentGroup.POST("findByIdentifier/", ac.FindByIdentifier())
	achievementContentGroup.PUT(":id/", ac.Update(), middlewares.AuthGuard(), middlewares.AdminGuard())
	achievementContentGroup.DELETE(":id/", ac.Delete(), middlewares.AuthGuard(), middlewares.AdminGuard())
}

// CRUD
func (ac *controller) Create() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var achievement content.Achievement
		if err := c.Bind(&achievement); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err := achievement.PrepareCreate(); err != nil {
			return err
		}

		if err := ac.Service.Create(ctx, &achievement); err != nil {
			return err
		}

		createdAchievement, err := ac.Service.FindByIdentifier(ctx, achievement.Identifier)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, createdAchievement.Sanitize())
	}
}

func (ac *controller) Find() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		achievement, err := ac.Service.Find(ctx, c.Param("id"))

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, achievement.Sanitize())
	}
}

func (ac *controller) FindAll() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		result, err := ac.Service.FindAll(ctx)
		if err != nil {
			return err
		}

		if result == nil {
			return c.JSON(http.StatusNoContent, nil)
		}

		for _, achievement := range result {
			achievement.Sanitize()
		}

		return c.JSON(http.StatusOK, result)
	}
}

func (ac *controller) FindByIdentifier() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var body map[string]interface{}

		if err := c.Bind(&body); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		identifierParam := strings.ToLower(strings.TrimSpace(body["identifier"].(string)))
		achievement, err := ac.Service.FindByIdentifier(ctx, identifierParam)

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, achievement.Sanitize())
	}
}

func (ac *controller) Update() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		achievement, err := ac.Service.Find(ctx, sanitizeString(c.Param("id")))
		if err != nil {
			return err
		}

		var newAchievement content.Achievement
		if err = c.Bind(&newAchievement); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}

		if err = achievement.PrepareUpdate(&newAchievement); err != nil {
			return err
		}

		if err = ac.Service.Update(ctx, achievement); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, achievement.Sanitize())
	}
}

func (ac *controller) Delete() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		if err := ac.Service.Delete(ctx, sanitizeString(c.Param("id"))); err != nil {
			return err
		}

		return c.JSON(http.StatusNoContent, nil)
	}
}

// Helper
func sanitizeString(str string) string {
	return strings.ToLower(strings.TrimSpace(str))
}
