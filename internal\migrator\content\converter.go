package content

import (
	"encoding/json"
	"errors"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Converter handles conversion of content between file format and database format
type Converter struct {
	Update func(oldValue interface{}, newValue interface{}, id string) (interface{}, error)
	Create func(newValue interface{}, id string) (interface{}, error)
}

// GetConverter returns a converter for the specified collection
func GetConverter(collection string) *Converter {
	var converter Converter
	switch collection {
	case repository.PRODUCTS_COLLECTION:
		converter = Converter{
			Update: func(oldValue interface{}, newValue interface{}, id string) (interface{}, error) {
				var oldProduct billing.Product
				var newProduct billing.Product

				marshal, err := json.Marshal(oldValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &oldProduct); err != nil {
					return nil, err
				}

				marshal, err = json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newProduct); err != nil {
					return nil, err
				}
				newProduct.ID = id
				newProduct.ObjectID, _ = primitive.ObjectIDFromHex(newProduct.ID)

				oldProduct.ID = id
				oldProduct.ObjectID, _ = primitive.ObjectIDFromHex(oldProduct.ID)

				err = oldProduct.PrepareUpdate(&newProduct)

				return &oldProduct, err
			},
			Create: func(newValue interface{}, id string) (interface{}, error) {
				var newProduct billing.Product

				marshal, err := json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newProduct); err != nil {
					return nil, err
				}

				newProduct.ID = id
				newProduct.ObjectID, _ = primitive.ObjectIDFromHex(newProduct.ID)

				err = newProduct.PrepareCreate()
				return newProduct, err
			},
		}

	case repository.ACHIEVEMENTS_COLLECTION:
		converter = Converter{
			Update: func(oldValue interface{}, newValue interface{}, id string) (interface{}, error) {
				var oldTrophy content.Achievement
				var newTrophy content.Achievement

				marshal, err := json.Marshal(oldValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &oldTrophy); err != nil {
					return nil, err
				}

				marshal, err = json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newTrophy); err != nil {
					return nil, err
				}

				newTrophy.ID = id
				newTrophy.ObjectID, _ = primitive.ObjectIDFromHex(newTrophy.ID)

				oldTrophy.ID = id
				oldTrophy.ObjectID, _ = primitive.ObjectIDFromHex(oldTrophy.ID)

				err = oldTrophy.PrepareUpdate(&newTrophy)

				return &oldTrophy, err
			},
			Create: func(newValue interface{}, id string) (interface{}, error) {
				var newTrophy content.Achievement

				marshal, err := json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newTrophy); err != nil {
					return nil, err
				}

				newTrophy.ID = id
				newTrophy.ObjectID, _ = primitive.ObjectIDFromHex(newTrophy.ID)

				err = newTrophy.PrepareCreate()
				return newTrophy, err
			},
		}

	case repository.TRAILS_COLLECTION:
		converter = Converter{
			Update: func(oldValue interface{}, newValue interface{}, id string) (interface{}, error) {
				var oldTrail content.Trail
				var newTrail content.Trail

				marshal, err := json.Marshal(oldValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &oldTrail); err != nil {
					return nil, err
				}

				marshal, err = json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newTrail); err != nil {
					return nil, err
				}

				newTrail.ID = id
				newTrail.ObjectID, _ = primitive.ObjectIDFromHex(newTrail.ID)

				oldTrail.ID = id
				oldTrail.ObjectID, _ = primitive.ObjectIDFromHex(oldTrail.ID)

				err = oldTrail.PrepareUpdate(&newTrail)

				return &oldTrail, err
			},
			Create: func(newValue interface{}, id string) (interface{}, error) {
				var newTrail content.Trail

				marshal, err := json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newTrail); err != nil {
					return nil, err
				}

				newTrail.ID = id
				newTrail.ObjectID, _ = primitive.ObjectIDFromHex(newTrail.ID)

				err = newTrail.PrepareCreate()
				return newTrail, err
			},
		}

	case repository.TRAILS_TUTORIAL_COLLECTION:
		converter = Converter{
			Update: func(oldValue interface{}, newValue interface{}, id string) (interface{}, error) {
				var oldTutorial content.Tutorial
				var newTutorial content.Tutorial

				marshal, err := json.Marshal(oldValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &oldTutorial); err != nil {
					return nil, err
				}

				marshal, err = json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newTutorial); err != nil {
					return nil, err
				}

				newTutorial.ID = id
				newTutorial.ObjectID, _ = primitive.ObjectIDFromHex(newTutorial.ID)

				oldTutorial.ID = id
				oldTutorial.ObjectID, _ = primitive.ObjectIDFromHex(oldTutorial.ID)

				err = oldTutorial.PrepareUpdate(&newTutorial)

				return &oldTutorial, err
			},
			Create: func(newValue interface{}, id string) (interface{}, error) {
				var newTutorial content.Tutorial

				marshal, err := json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newTutorial); err != nil {
					return nil, err
				}

				newTutorial.ID = id
				newTutorial.ObjectID, _ = primitive.ObjectIDFromHex(newTutorial.ID)

				err = newTutorial.PrepareCreate()
				return newTutorial, err
			},
		}

	case repository.TRAILS_EXTRA_COLLECTION:
		converter = Converter{
			Update: func(oldValue interface{}, newValue interface{}, id string) (interface{}, error) {
				var oldTrailExtra content.Trail
				var newTrailExtra content.Trail

				marshal, err := json.Marshal(oldValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &oldTrailExtra); err != nil {
					return nil, err
				}

				marshal, err = json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newTrailExtra); err != nil {
					return nil, err
				}

				newTrailExtra.ID = id
				newTrailExtra.ObjectID, _ = primitive.ObjectIDFromHex(newTrailExtra.ID)

				oldTrailExtra.ID = id
				oldTrailExtra.ObjectID, _ = primitive.ObjectIDFromHex(oldTrailExtra.ID)

				err = oldTrailExtra.PrepareUpdate(&newTrailExtra)

				return &oldTrailExtra, err
			},
			Create: func(newValue interface{}, id string) (interface{}, error) {
				var newTrailExtra content.Trail

				marshal, err := json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newTrailExtra); err != nil {
					return nil, err
				}

				newTrailExtra.ID = id
				newTrailExtra.ObjectID, _ = primitive.ObjectIDFromHex(newTrailExtra.ID)

				err = newTrailExtra.PrepareCreate()
				return newTrailExtra, err
			},
		}

	case repository.TICKERS_COLLECTION:
		converter = Converter{
			Update: func(oldValue interface{}, newValue interface{}, id string) (interface{}, error) {
				var oldTicker content.Ticker
				var newTicker content.Ticker

				marshal, err := json.Marshal(oldValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &oldTicker); err != nil {
					return nil, err
				}

				marshal, err = json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newTicker); err != nil {
					return nil, err
				}

				newTicker.ID = id
				newTicker.ObjectID, _ = primitive.ObjectIDFromHex(newTicker.ID)

				oldTicker.ID = id
				oldTicker.ObjectID, _ = primitive.ObjectIDFromHex(oldTicker.ID)

				err = oldTicker.PrepareUpdate(&newTicker)

				return &oldTicker, err
			},
			Create: func(newValue interface{}, id string) (interface{}, error) {
				var newTicker content.Ticker

				marshal, err := json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newTicker); err != nil {
					return nil, err
				}

				newTicker.ID = id
				newTicker.ObjectID, _ = primitive.ObjectIDFromHex(newTicker.ID)

				err = newTicker.PrepareCreate()
				return newTicker, err
			},
		}

	case repository.INVESTMENT_CATEGORIES_COLLECTION:
		converter = Converter{
			Update: func(oldValue interface{}, newValue interface{}, id string) (interface{}, error) {
				var oldInvestmentCategory content.InvestmentCategory
				var newInvestmentCategory content.InvestmentCategory

				marshal, err := json.Marshal(oldValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &oldInvestmentCategory); err != nil {
					return nil, err
				}

				marshal, err = json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newInvestmentCategory); err != nil {
					return nil, err
				}

				newInvestmentCategory.ID = id
				newInvestmentCategory.ObjectID, _ = primitive.ObjectIDFromHex(newInvestmentCategory.ID)

				oldInvestmentCategory.ID = id
				oldInvestmentCategory.ObjectID, _ = primitive.ObjectIDFromHex(oldInvestmentCategory.ID)

				err = oldInvestmentCategory.PrepareUpdate(&newInvestmentCategory)

				return &oldInvestmentCategory, err
			},
			Create: func(newValue interface{}, id string) (interface{}, error) {
				var newInvestmentCategory content.InvestmentCategory

				marshal, err := json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newInvestmentCategory); err != nil {
					return nil, err
				}

				newInvestmentCategory.ID = id
				newInvestmentCategory.ObjectID, _ = primitive.ObjectIDFromHex(newInvestmentCategory.ID)

				err = newInvestmentCategory.PrepareCreate()
				return newInvestmentCategory, err
			},
		}

	case repository.WALLETS_COLLECTION:
		converter = Converter{
			Update: func(oldValue interface{}, newValue interface{}, id string) (interface{}, error) {
				var oldWallet content.Wallet
				var newWallet content.Wallet

				marshal, err := json.Marshal(oldValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &oldWallet); err != nil {
					return nil, err
				}

				marshal, err = json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newWallet); err != nil {
					return nil, err
				}

				newWallet.ID = id
				newWallet.ObjectID, _ = primitive.ObjectIDFromHex(newWallet.ID)

				oldWallet.ID = id
				oldWallet.ObjectID, _ = primitive.ObjectIDFromHex(oldWallet.ID)

				err = oldWallet.PrepareUpdate(&newWallet)

				return &oldWallet, err
			},
			Create: func(newValue interface{}, id string) (interface{}, error) {
				var newWallet content.Wallet

				marshal, err := json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newWallet); err != nil {
					return nil, err
				}

				newWallet.ID = id
				newWallet.ObjectID, _ = primitive.ObjectIDFromHex(newWallet.ID)

				err = newWallet.PrepareCreate()
				return newWallet, err
			},
		}

	case repository.USERS_COLLECTION:
		converter = Converter{
			Update: func(oldValue interface{}, newValue interface{}, id string) (interface{}, error) {
				var oldUser model.User
				var newUser model.User

				marshal, err := json.Marshal(oldValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &oldUser); err != nil {
					return nil, err
				}

				marshal, err = json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newUser); err != nil {
					return nil, err
				}

				newUser.ID = id
				newUser.ObjectID, _ = primitive.ObjectIDFromHex(newUser.ID)

				oldUser.ID = id
				oldUser.ObjectID, _ = primitive.ObjectIDFromHex(oldUser.ID)

				err = oldUser.PrepareUpdate(&newUser)

				return &oldUser, err
			},
			Create: func(newValue interface{}, id string) (interface{}, error) {
				var newUser model.User

				marshal, err := json.Marshal(newValue)
				if err != nil {
					return nil, err
				}

				if err = json.Unmarshal(marshal, &newUser); err != nil {
					return nil, err
				}

				newUser.ID = id
				newUser.ObjectID, _ = primitive.ObjectIDFromHex(newUser.ID)

				err = newUser.PrepareCreate()
				return newUser, err
			},
		}

	default:
		converter = Converter{
			Update: func(oldValue interface{}, newValue interface{}, id string) (interface{}, error) {
				return nil, errors.New("collection not implemented")
			},
			Create: func(newValue interface{}, id string) (interface{}, error) {
				return nil, errors.New("collection not implemented")
			},
		}
	}
	return &converter
}
