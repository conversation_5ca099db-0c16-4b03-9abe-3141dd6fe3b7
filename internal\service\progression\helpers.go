package progression

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
)

// loadUserData loads all the necessary user data for progression operations
func (s *service) loadUserData(ctx context.Context, userId string, userClassification string, contractIDs []string, progressionBody *progression.ProgressionBody) (
	*progression.Progression, *model.Vault, *content.Trail, error) {

	// Find user progression
	foundProgression, err := s.FindByUser(ctx, userId)
	if err != nil {
		return nil, nil, nil, err
	}

	// Find user vault
	foundVault, err := s.VaultService.FindByUser(ctx, userId)
	if err != nil {
		return nil, nil, nil, err
	}

	// Find all trail content
	trailContent, err := s.TrailService.Find(ctx, progressionBody.Trail)
	if err != nil {
		return nil, nil, nil, err
	}

	if !trailContent.HasAccess(userClassification, contractIDs) {
		return nil, nil, nil, errors.New(errors.Service, "access denied", errors.Forbidden, nil)
	}

	return foundProgression, foundVault, trailContent, nil
}

// validateAndPrepareProgression validates the progression and prepares it for registration
func (s *service) validateAndPrepareProgression(
	ctx context.Context,
	userProgression *progression.Progression,
	progressionBody *progression.ProgressionBody,
	trailContent *content.Trail) error {

	// Validate the progression body
	if err := progressionBody.Validate(); err != nil {
		return err
	}

	// Validate user progression against content requirements
	if err := userProgression.ValidateUserProgression(progressionBody, trailContent); err != nil {
		return err
	}

	// Set timestamp
	progressionBody.Timestamp = time.Now()

	return nil
}

// checkTrailCompletionAndAwardAchievement checks if a trail is completed and awards a achievement if applicable
func (s *service) checkTrailCompletionAndAwardAchievement(
	ctx context.Context,
	userProgression *progression.Progression,
	userVault *model.Vault,
	trailContent *content.Trail) (bool, error) {

	updateVault := false
	trailProgress := userProgression.GetTrailProgression(trailContent.ID)

	// Check if trail is completed
	if trailProgress.Total == TrailCompletionThreshold {
		// Find and award trophy
		foundTrophy, err := s.AchievementService.FindByRequirement(ctx, trailContent.ID)
		if err != nil {
			// Log error but don't block progress
		} else if foundTrophy != nil {
			userProgression.RegisterAchievement(foundTrophy)
		}

		// Award coins for trail completion if not already rewarded
		if !trailProgress.Rewarded {
			userVault.Coins += TrailCompletionReward
			trailProgress.Rewarded = true
			updateVault = true
		}
	}

	return updateVault, nil
}

// updateUserVaultIfNeeded updates the user's vault if changes were made
func (s *service) updateUserVaultIfNeeded(ctx context.Context, userVault *model.Vault, updateNeeded bool) error {
	if updateNeeded {
		if err := s.VaultService.Update(ctx, userVault); err != nil {
			return errors.New(errors.Service, "failed to update user vault", errors.Internal, err)
		}
	}
	return nil
}

// getLessonProgress gets or creates a lesson progress object
func getLessonProgress(foundProgression *progression.Progression, trailId string, lessonContent *content.Lesson) *progression.Lesson {
	// Try to get existing lesson progress
	lessonProgression := foundProgression.GetLessonProgression(trailId, lessonContent.Identifier)
	if lessonProgression != nil {
		return lessonProgression
	}

	// Create new lesson progress
	completed := false
	available := true
	rewarded := false

	// Check if lesson is available based on requirements
	for _, requirement := range lessonContent.Requirements {
		if lessonRequirementProgression := foundProgression.GetLessonProgression(trailId, requirement); lessonRequirementProgression == nil || !lessonRequirementProgression.Completed {
			available = false
			break
		}
	}

	return &progression.Lesson{
		Identifier: lessonContent.Identifier,
		Completed:  completed,
		Available:  available,

		Rewarded: rewarded,
	}
}

// getTrailProgress gets or creates a trail progress object
func getTrailProgress(foundProgression *progression.Progression, trailContent *content.Trail) *progression.Trail {
	// Try to get existing trail progress
	trailProgress := foundProgression.GetTrailProgression(trailContent.ID)
	if trailProgress != nil {
		foundProgression.UpdateTrailTotal(trailContent)
		return trailProgress
	}

	// Create new trail progress
	available := true

	// Check if trail is available based on requirements
	for _, requirement := range trailContent.Requirements {
		if trailRequirementProgress := foundProgression.GetTrailProgression(requirement); trailRequirementProgress == nil || trailRequirementProgress.Total < TrailRequirementThreshold {
			available = false
			break
		}
	}

	return &progression.Trail{
		ID:        trailContent.ID,
		Available: available,
	}
}

func getChallengeProgress(foundProgression *progression.Progression, trailContent *content.Trail, challengeContent *content.Challenge) *progression.Challenge {
	// Add the option that if all the phases are completed than add complete and rewarded
	completed := false
	available := false
	rewarded := false

	challengeProgression := foundProgression.GetChallengeProgression(trailContent.ID)

	if challengeProgression != nil {
		//foundProgression.UpdateChallengeStatus(,challengeContent)
		foundProgression.UpdateChallengeTotal(trailContent)
		if challengeProgression.Completed {
			challengeProgression.Rewarded = true
		} else {
			challengeProgression.Rewarded = false
		}
		// 	// 	rewarded = challengeProgression.Rewarded
		return challengeProgression
	}

	trailProgression := foundProgression.GetTrailProgression(trailContent.ID)
	if trailProgression != nil {
		available = !challengeContent.Locked && trailProgression.LessonsCompleted
	}

	return &progression.Challenge{
		Identifier: challengeContent.Identifier,
		Completed:  completed,
		Available:  available,
		Rewarded:   rewarded,
	}
}

func getChallengePhaseProgress(foundProgression *progression.Progression, trailContent *content.Trail, challengeContent *content.Challenge, phaseContent *content.ChallengePhase) *progression.ChallengePhase {
	challengeProgression := foundProgression.GetChallengeProgression(trailContent.ID)
	available := true

	if challengeProgression != nil && challengeProgression.Phases != nil && len(challengeProgression.Phases) > 0 {
		for _, phase := range challengeProgression.Phases {
			if phase.Identifier == phaseContent.Identifier {
				return phase
			}
		}
		if len(phaseContent.Requirements) > 0 {
			for _, requirement := range phaseContent.Requirements {
				for _, phase := range challengeProgression.Phases {
					if phase.Identifier == requirement && !phase.Completed {
						available = false
					}
				}
			}
		}
		// else if !progression.IsFirstPhaseOfChallenge(challengeContent, phaseContent.Identifier) {
		// 	available = false
		// }

		return &progression.ChallengePhase{
			Identifier: phaseContent.Identifier,
			Available:  available,
		}
	}
	// else if !progression.IsFirstPhaseOfChallenge(challengeContent, phaseContent.Identifier) {
	// 	available = false
	// }

	return &progression.ChallengePhase{
		Identifier: phaseContent.Identifier,
		Available:  available,
	}
}

func filterAccessibleTrails(trailCards []*content.TrailCard, userClassification string, contractIDs []string) []*content.TrailCard {
	filteredCards := make([]*content.TrailCard, 0, len(trailCards))
	for _, card := range trailCards {
		if card.HasAccess(userClassification, contractIDs) {
			filteredCards = append(filteredCards, card)
		}
	}
	return filteredCards
}
