package content

import (
	"github.com/dsoplabs/dinbora-backend/internal/errors"
)

var (
	ErrTrailInvalidID          = errors.OldError("trail id is invalid", errors.NotFound, nil)
	ErrTrailRequiredName       = errors.OldError("trail name is a required field", errors.Validation, nil)
	ErrTrailRequiredIdentifier = errors.OldError("trail identifier is a required field", errors.Validation, nil)
	ErrTrailRequiredLogo       = errors.OldError("trail logo is a required field", errors.Validation, nil)
	ErrTrailRequiredLessons    = errors.OldError("trail lessons is a required field", errors.Validation, nil)
	ErrTrailRequiredChallenge  = errors.OldError("trail challenge is a required field", errors.Validation, nil)

	ErrTutorialInvalidID   = errors.OldError("tutorial id is invalid", errors.NotFound, nil)
	ErrTrailRequiredTicker = errors.OldError("tutorial ticker is a required field", errors.Validation, nil)
)

var (
	ErrTrailExtraInvalidID             = errors.OldError("trail extra id is invalid", errors.NotFound, nil)
	ErrTrailExtraRequiredName          = errors.OldError("trail extra name is a required field", errors.Validation, nil)
	ErrTrailExtraRequiredIdentifier    = errors.OldError("trail extra identifier is a required field", errors.Validation, nil)
	ErrTrailExtraRequiredDescription   = errors.OldError("trail extra description is a required field", errors.Validation, nil)
	ErrTrailExtraRequiredLogo          = errors.OldError("trail extra logo is a required field", errors.Validation, nil)
	ErrTrailExtraRequiredLessons       = errors.OldError("trail extra lessons is a required field", errors.Validation, nil)
	ErrTrailExtraRequiredAccessControl = errors.OldError("trail extra access control is a required field", errors.Validation, nil)
)

var (
	ErrAccessControlRequiredType        = errors.OldError("access control type is a required field", errors.Validation, nil)
	ErrAccessControlInvalidType         = errors.OldError("access control type must be one of CORPORATE, SUBSCRIPTION, or PREMIUM", errors.Validation, nil)
	ErrAccessControlRequiredContractIDs = errors.OldError("access control contract IDs are required for corporate access type", errors.Validation, nil)
	ErrAccessControlInvalidDateRange    = errors.OldError("access control valid until must be after valid from", errors.Validation, nil)
)

var (
	ErrLessonRequiredName       = errors.OldError("lesson name is a required field", errors.Validation, nil)
	ErrLessonRequiredLogo       = errors.OldError("lesson logo is a required field", errors.Validation, nil)
	errLessonRequiredIdentifier = errors.OldError("lesson identifier is a required field", errors.Validation, nil)
	ErrLessonContentRequired    = errors.OldError("lesson content is a required field", errors.Validation, nil)
)

var (
	ErrLessonContentIdentifierShouldBeUnique       = errors.OldError("lesson content identifier field should be unique", errors.Conflict, nil)
	ErrLessonContentRequiredDescription            = errors.OldError("lesson content description is a required field", errors.Validation, nil)
	ErrLessonContentRequiredIdentifier             = errors.OldError("lesson content identifier is a required field", errors.Validation, nil)
	ErrLessonContentOrLessonChoiceShouldHaveNext   = errors.OldError("lesson content or lesson choice should have required field next", errors.Validation, nil)
	ErrLessonContentOrLessonChoiceNextInvalidValue = errors.OldError("lesson content or lesson choice should point to valid identifier", errors.NotFound, nil)
)

var (
	ErrLessonChoiceRequiredName       = errors.OldError("lesson choice name is a required field", errors.Validation, nil)
	ErrLessonChoiceRequiredIdentifier = errors.OldError("lesson choice identifier is a required field", errors.Validation, nil)
)

var (
	ErrChallengePhaseRequiredName        = errors.OldError("challenge phase name is a required field", errors.Validation, nil)
	ErrChallengePhaseRequiredIdentifier  = errors.OldError("challenge phase identifier is a required field", errors.Validation, nil)
	ErrChallengePhaseRequiredLogo        = errors.OldError("challenge phase logo is a required field", errors.Validation, nil)
	ErrChallengePhaseRequiredContent     = errors.OldError("challenge phase content is a required field", errors.Validation, nil)
	ErrChallengePhaseRequiredRequirement = errors.OldError("challenge phase requirement is a required field", errors.Validation, nil)
)

var (
	ErrChallengeRequiredName       = errors.OldError("challenge name is a required field", errors.Validation, nil)
	ErrChallengeRequiredIdentifier = errors.OldError("challenge identifier is a required field", errors.Validation, nil)
	ErrChallengeRequiredLogo       = errors.OldError("challenge logo is a required field", errors.Validation, nil)
	ErrChallengeRequiredPhases     = errors.OldError("challenge phases is a required field", errors.Validation, nil)
)

var (
	ErrChallengePhaseContentIdentifierShouldBeUnique     = errors.OldError("challenge content identifier field should be unique", errors.Conflict, nil)
	ErrChallengeContentRequiredDescription               = errors.OldError("challenge content description is a required field", errors.Validation, nil)
	ErrChallengeContentRequiredIdentifier                = errors.OldError("challenge content identifier is a required field", errors.Validation, nil)
	ErrChallengeContentOrChallengeChoiceShouldHaveNext   = errors.OldError("challenge content or challenge choice should have required field next", errors.Validation, nil)
	ErrChallengeContentOrChallengeChoiceNextInvalidValue = errors.OldError("challenge content or challenge choice should point to valid identifier", errors.NotFound, nil)
)

var (
	ErrChallengeChoiceRequiredName       = errors.OldError("challenge choice name is a required field", errors.Validation, nil)
	ErrChallengeChoiceRequiredIdentifier = errors.OldError("challenge choice identifier is a required field", errors.Validation, nil)
	ErrChallengeChoiceTypeInvalid        = errors.OldError("challenge choice must be one of TEXT, NUMBER, OPTIONS or DATE", errors.Validation, nil)
)

var (
	ErrTrophyInvalidID           = errors.OldError("achievement id is invalid", errors.NotFound, nil)
	ErrTrophyRequiredName        = errors.OldError("achievement name is a required field", errors.Validation, nil)
	ErrTrophyRequiredIdentifier  = errors.OldError("achievement identifier is a required field", errors.Validation, nil)
	ErrTrophyRequiredRequirement = errors.OldError("achievement requirement is a required field", errors.Validation, nil)
)

var (
	ErrTickerRequiredName       = errors.OldError("ticker name is a required field", errors.Validation, nil)
	ErrTickerRequiredIdentifier = errors.OldError("ticker identifier is a required field", errors.Validation, nil)
	ErrTickerRequiredType       = errors.OldError("ticker type is a required field", errors.Validation, nil)
	ErrTickerRequiredGroup      = errors.OldError("ticker group is a required field", errors.Validation, nil)
	ErrTickerRequiredCategory   = errors.OldError("ticker category is a required field", errors.Validation, nil)
	ErrTickerInvalidId          = errors.OldError("ticker id is invalid", errors.NotFound, nil)
)

var (
	ErrInvestmentCategoryRequiredName       = errors.OldError("investment category name is a required field", errors.Validation, nil)
	ErrInvestmentCategoryRequiredIdentifier = errors.OldError("investment category identifier is a required field", errors.Validation, nil)
	ErrInvestmentCategoryRequiredLogo       = errors.OldError("investment category logo is a required field", errors.Validation, nil)
	ErrInvestmentCategoryInvalidId          = errors.OldError("investment category id is invalid", errors.NotFound, nil)
)

var (
	ErrWalletGroupRequiredName     = errors.OldError("wallet group name is a required field", errors.Validation, nil)
	ErrWalletGroupRequiredCategory = errors.OldError("wallet group category is a required field", errors.Validation, nil)
)

var (
	ErrWalletRequiredName       = errors.OldError("wallet name is a required field", errors.Validation, nil)
	ErrWalletRequiredIdentifier = errors.OldError("wallet identifier is a required field", errors.Validation, nil)
	ErrWalletRequiredGroups     = errors.OldError("wallet groups is a required field", errors.Validation, nil)
	ErrWalletInvalidId          = errors.OldError("wallet id is invalid", errors.NotFound, nil)
)
